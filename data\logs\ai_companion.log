2025-07-11 16:31:47 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:31:47 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:38:05 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:38:05 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:39:27 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:39:27 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:39:27 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:39:27 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:39:27 - ai_companion.main - INFO - 🚀 Initializing AI Companion System...
2025-07-11 16:39:27 - ai_companion.main - INFO - ✅ Configuration validated
2025-07-11 16:39:27 - ai_companion.main - INFO - 🧠 Initializing core AI services...
2025-07-11 16:39:27 - ai_companion.services.gemini - INFO - ✅ Gemini model 'gemini-pro' initialized successfully
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_profiles")
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("personal_memories")
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("personal_memories")
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("universal_memories")
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - PRAGMA temp.table_info("universal_memories")
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversations")
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE personal_memories (
	memory_id VARCHAR NOT NULL, 
	user_id VARCHAR, 
	content TEXT, 
	keywords JSON, 
	emotional_context JSON, 
	importance_score FLOAT, 
	interaction_type VARCHAR, 
	timestamp DATETIME, 
	last_accessed DATETIME, 
	access_count INTEGER, 
	PRIMARY KEY (memory_id)
)


2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - [no key 0.00239s] ()
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - CREATE INDEX ix_personal_memories_user_id ON personal_memories (user_id)
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - [no key 0.00109s] ()
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - 
CREATE TABLE universal_memories (
	memory_id VARCHAR NOT NULL, 
	content TEXT, 
	keywords JSON, 
	emotional_patterns JSON, 
	usage_count INTEGER, 
	effectiveness_score FLOAT, 
	timestamp DATETIME, 
	last_used DATETIME, 
	PRIMARY KEY (memory_id)
)


2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - [no key 0.00204s] ()
2025-07-11 16:39:27 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-11 16:39:27 - ai_companion.services.storage - INFO - ✅ Database initialized at data\db\ai_companion.db
2025-07-11 16:39:27 - ai_companion.main - ERROR - ❌ Failed to initialize system: GUILT
2025-07-11 16:40:27 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:40:27 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:40:27 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:40:27 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:40:27 - ai_companion.main - INFO - 🚀 Initializing AI Companion System...
2025-07-11 16:40:27 - ai_companion.main - INFO - ✅ Configuration validated
2025-07-11 16:40:27 - ai_companion.main - INFO - 🧠 Initializing core AI services...
2025-07-11 16:40:27 - ai_companion.services.gemini - INFO - ✅ Gemini model 'gemini-pro' initialized successfully
2025-07-11 16:40:27 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 16:40:27 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_profiles")
2025-07-11 16:40:27 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:40:27 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("personal_memories")
2025-07-11 16:40:27 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:40:27 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("universal_memories")
2025-07-11 16:40:27 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:40:27 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversations")
2025-07-11 16:40:27 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:40:27 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-11 16:40:27 - ai_companion.services.storage - INFO - ✅ Database initialized at data\db\ai_companion.db
2025-07-11 16:40:27 - ai_companion.core.emotions - INFO - ✅ Emotional Intelligence Service initialized
2025-07-11 16:40:29 - ai_companion.services.gemini - ERROR - Error generating response: 404 models/gemini-pro is not found for API version v1beta, or is not supported for generateContent. Call ListModels to see the list of available models and their supported methods.
2025-07-11 16:40:29 - ai_companion.main - ERROR - ❌ Service testing failed: Gemini service test failed
2025-07-11 16:40:29 - ai_companion.main - ERROR - ❌ Failed to initialize system: Gemini service test failed
2025-07-11 16:41:19 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:41:19 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:41:19 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:41:19 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:41:19 - ai_companion.main - INFO - 🚀 Initializing AI Companion System...
2025-07-11 16:41:19 - ai_companion.main - INFO - ✅ Configuration validated
2025-07-11 16:41:19 - ai_companion.main - INFO - 🧠 Initializing core AI services...
2025-07-11 16:41:19 - ai_companion.services.gemini - INFO - ✅ Gemini model 'gemini-1.5-flash' initialized successfully
2025-07-11 16:41:19 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 16:41:19 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_profiles")
2025-07-11 16:41:19 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:41:19 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("personal_memories")
2025-07-11 16:41:19 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:41:19 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("universal_memories")
2025-07-11 16:41:19 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:41:19 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversations")
2025-07-11 16:41:19 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:41:19 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-11 16:41:19 - ai_companion.services.storage - INFO - ✅ Database initialized at data\db\ai_companion.db
2025-07-11 16:41:19 - ai_companion.core.emotions - INFO - ✅ Emotional Intelligence Service initialized
2025-07-11 16:41:22 - ai_companion.core.memory - ERROR - Error storing memory for user test_user: 'StorageService' object has no attribute 'store_memory'
2025-07-11 16:41:22 - ai_companion.main - ERROR - ❌ Service testing failed: 'StorageService' object has no attribute 'store_memory'
2025-07-11 16:41:22 - ai_companion.main - ERROR - ❌ Failed to initialize system: 'StorageService' object has no attribute 'store_memory'
2025-07-11 16:43:05 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:43:05 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:43:05 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:43:05 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:43:05 - ai_companion.main - INFO - 🚀 Initializing AI Companion System...
2025-07-11 16:43:05 - ai_companion.main - INFO - ✅ Configuration validated
2025-07-11 16:43:05 - ai_companion.main - INFO - 🧠 Initializing core AI services...
2025-07-11 16:43:05 - ai_companion.services.gemini - INFO - ✅ Gemini model 'gemini-1.5-flash' initialized successfully
2025-07-11 16:43:05 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 16:43:05 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_profiles")
2025-07-11 16:43:05 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:43:05 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("personal_memories")
2025-07-11 16:43:05 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:43:05 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("universal_memories")
2025-07-11 16:43:05 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:43:05 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversations")
2025-07-11 16:43:05 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:43:05 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-11 16:43:05 - ai_companion.services.storage - INFO - ✅ Database initialized at data\db\ai_companion.db
2025-07-11 16:43:05 - ai_companion.core.emotions - INFO - ✅ Emotional Intelligence Service initialized
2025-07-11 16:43:08 - ai_companion.services.storage - ERROR - Error storing personal memory: 'PersonalMemory' object has no attribute 'memory_id'
2025-07-11 16:43:08 - ai_companion.services.gemini - ERROR - Error analyzing emotional content: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 51
}
]
2025-07-11 16:43:08 - ai_companion.main - INFO - ✅ All core services tested successfully
2025-07-11 16:43:08 - ai_companion.main - INFO - ✅ Core services initialized
2025-07-11 16:43:08 - ai_companion.main - INFO - 🏥 Initializing mental health services...
2025-07-11 16:43:08 - ai_companion.mental_health.crisis_detection - INFO - ✅ Crisis Detection Service initialized
2025-07-11 16:43:08 - ai_companion.mental_health.privacy - WARNING - Generated new encryption key - store this securely in production!
2025-07-11 16:43:08 - ai_companion.mental_health.privacy - INFO - ✅ Data Anonymizer initialized
2025-07-11 16:43:08 - ai_companion.mental_health.analytics - INFO - ✅ Mental Health Analytics Service initialized
2025-07-11 16:43:08 - ai_companion.main - INFO - ✅ Mental health services initialized
2025-07-11 16:43:08 - ai_companion.main - INFO - 🌐 Initializing interfaces...
2025-07-11 16:43:09 - ai_companion.interfaces.gradio_app - INFO - ✅ Gradio interface initialized
2025-07-11 16:43:09 - ai_companion.main - INFO - ✅ Interfaces initialized
2025-07-11 16:43:09 - ai_companion.main - INFO - 🎉 AI Companion System ready in 3.89 seconds!
2025-07-11 16:43:09 - ai_companion.services.gemini - ERROR - Error generating response: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 50
}
]
2025-07-11 16:43:10 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-11 16:43:11 - ai_companion.services.gemini - ERROR - Error analyzing emotional content: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 49
}
]
2025-07-11 16:43:11 - ai_companion.services.storage - ERROR - Error storing personal memory: 'PersonalMemory' object has no attribute 'memory_id'
2025-07-11 16:43:11 - ai_companion.services.gemini - ERROR - Error analyzing emotional content: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 49
}
]
2025-07-11 16:43:12 - ai_companion.services.gemini - ERROR - Error generating therapeutic response: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 48
}
]
2025-07-11 16:43:12 - ai_companion.mental_health.crisis_detection - WARNING - Crisis event tracked for user test_user: suicide_ideation
2025-07-11 16:43:12 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 16:43:12 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM user_profiles
2025-07-11 16:43:12 - sqlalchemy.engine.Engine - INFO - [generated in 0.00149s] ()
2025-07-11 16:43:12 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM personal_memories
2025-07-11 16:43:12 - sqlalchemy.engine.Engine - INFO - [generated in 0.00139s] ()
2025-07-11 16:43:12 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM universal_memories
2025-07-11 16:43:12 - sqlalchemy.engine.Engine - INFO - [generated in 0.00109s] ()
2025-07-11 16:43:12 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM conversations
2025-07-11 16:43:12 - sqlalchemy.engine.Engine - INFO - [generated in 0.00151s] ()
2025-07-11 16:43:12 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-11 16:43:12 - ai_companion.main - INFO - 🛑 Shutting down AI Companion System...
2025-07-11 16:43:12 - ai_companion.services.storage - INFO - Storage connections closed
2025-07-11 16:43:12 - ai_companion.main - INFO - ✅ System shutdown complete
2025-07-11 16:45:27 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:45:27 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:45:27 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:45:27 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:45:27 - ai_companion.main - INFO - 🚀 Initializing AI Companion System...
2025-07-11 16:45:27 - ai_companion.main - INFO - ✅ Configuration validated
2025-07-11 16:45:27 - ai_companion.main - INFO - 🧠 Initializing core AI services...
2025-07-11 16:45:27 - ai_companion.services.gemini - INFO - ✅ Gemini model 'gemini-1.5-flash' initialized successfully
2025-07-11 16:45:27 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 16:45:27 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_profiles")
2025-07-11 16:45:27 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:45:27 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("personal_memories")
2025-07-11 16:45:27 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:45:27 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("universal_memories")
2025-07-11 16:45:27 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:45:27 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversations")
2025-07-11 16:45:27 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:45:27 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-11 16:45:27 - ai_companion.services.storage - INFO - ✅ Database initialized at data\db\ai_companion.db
2025-07-11 16:45:27 - ai_companion.core.emotions - INFO - ✅ Emotional Intelligence Service initialized
2025-07-11 16:45:29 - ai_companion.services.gemini - ERROR - Error generating response: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 31
}
]
2025-07-11 16:45:29 - ai_companion.main - ERROR - ❌ Service testing failed: Gemini service test failed
2025-07-11 16:45:29 - ai_companion.main - ERROR - ❌ Failed to initialize system: Gemini service test failed
2025-07-11 16:46:26 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:46:26 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:46:26 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:46:26 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:46:26 - ai_companion.main - INFO - 🚀 Initializing AI Companion System...
2025-07-11 16:46:26 - ai_companion.main - INFO - ✅ Configuration validated
2025-07-11 16:46:26 - ai_companion.main - INFO - 🧠 Initializing core AI services...
2025-07-11 16:46:26 - ai_companion.services.gemini - INFO - ✅ Gemini model 'gemini-1.5-flash' initialized successfully
2025-07-11 16:46:26 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 16:46:26 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_profiles")
2025-07-11 16:46:26 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:46:26 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("personal_memories")
2025-07-11 16:46:26 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:46:26 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("universal_memories")
2025-07-11 16:46:26 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:46:26 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversations")
2025-07-11 16:46:26 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:46:26 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-11 16:46:26 - ai_companion.services.storage - INFO - ✅ Database initialized at data\db\ai_companion.db
2025-07-11 16:46:26 - ai_companion.core.emotions - INFO - ✅ Emotional Intelligence Service initialized
2025-07-11 16:46:27 - ai_companion.services.gemini - ERROR - Error generating response: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 33
}
]
2025-07-11 16:46:27 - ai_companion.main - WARNING - ⚠️ Gemini service test failed - may be quota limited
2025-07-11 16:46:27 - ai_companion.services.storage - ERROR - Error storing personal memory: 'PersonalMemory' object has no attribute 'keywords'
2025-07-11 16:46:31 - ai_companion.main - INFO - ✅ All core services tested successfully
2025-07-11 16:46:31 - ai_companion.main - INFO - ✅ Core services initialized
2025-07-11 16:46:31 - ai_companion.main - INFO - 🏥 Initializing mental health services...
2025-07-11 16:46:31 - ai_companion.mental_health.crisis_detection - INFO - ✅ Crisis Detection Service initialized
2025-07-11 16:46:31 - ai_companion.mental_health.privacy - WARNING - Generated new encryption key - store this securely in production!
2025-07-11 16:46:31 - ai_companion.mental_health.privacy - INFO - ✅ Data Anonymizer initialized
2025-07-11 16:46:31 - ai_companion.mental_health.analytics - INFO - ✅ Mental Health Analytics Service initialized
2025-07-11 16:46:31 - ai_companion.main - INFO - ✅ Mental health services initialized
2025-07-11 16:46:31 - ai_companion.main - INFO - 🌐 Initializing interfaces...
2025-07-11 16:46:31 - ai_companion.interfaces.gradio_app - INFO - ✅ Gradio interface initialized
2025-07-11 16:46:31 - ai_companion.main - INFO - ✅ Interfaces initialized
2025-07-11 16:46:31 - ai_companion.main - INFO - 🎉 AI Companion System ready in 5.21 seconds!
2025-07-11 16:46:32 - ai_companion.services.gemini - ERROR - Error generating response: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 28
}
]
2025-07-11 16:46:33 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-11 16:46:33 - ai_companion.services.gemini - ERROR - Error analyzing emotional content: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 27
}
]
2025-07-11 16:46:33 - ai_companion.services.storage - ERROR - Error storing personal memory: 'PersonalMemory' object has no attribute 'keywords'
2025-07-11 16:46:33 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 16:46:33 - sqlalchemy.engine.Engine - INFO - SELECT * FROM personal_memories WHERE user_id = ? ORDER BY importance_score DESC, timestamp DESC LIMIT ?
2025-07-11 16:46:33 - sqlalchemy.engine.Engine - INFO - [generated in 0.00120s] ('test_user', 5)
2025-07-11 16:46:34 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-11 16:46:34 - ai_companion.core.conversation - INFO - Started conversation 0fe60c7c-9273-46bb-a568-74d0714c2407 for user test_user
2025-07-11 16:46:34 - ai_companion.services.gemini - ERROR - Error analyzing emotional content: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 25
}
]
2025-07-11 16:46:34 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 16:46:34 - sqlalchemy.engine.Engine - INFO - SELECT * FROM universal_memories ORDER BY effectiveness_score DESC, usage_count DESC LIMIT ?
2025-07-11 16:46:34 - sqlalchemy.engine.Engine - INFO - [generated in 0.00100s] (20,)
2025-07-11 16:46:34 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-11 16:46:36 - ai_companion.services.gemini - ERROR - Error generating response: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 24
}
]
2025-07-11 16:46:36 - ai_companion.core.conversation - ERROR - Error processing message for user test_user: 1 validation error for ConversationMessage
content
  Input should be a valid string [type=string_type, input_value=None, input_type=NoneType]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
2025-07-11 16:46:36 - ai_companion.services.gemini - ERROR - Error analyzing emotional content: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 24
}
]
2025-07-11 16:46:37 - ai_companion.services.gemini - ERROR - Error generating therapeutic response: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 22
}
]
2025-07-11 16:46:37 - ai_companion.mental_health.crisis_detection - WARNING - Crisis event tracked for user test_user: suicide_ideation
2025-07-11 16:46:37 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 16:46:37 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM user_profiles
2025-07-11 16:46:37 - sqlalchemy.engine.Engine - INFO - [generated in 0.00128s] ()
2025-07-11 16:46:37 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM personal_memories
2025-07-11 16:46:37 - sqlalchemy.engine.Engine - INFO - [generated in 0.00111s] ()
2025-07-11 16:46:37 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM universal_memories
2025-07-11 16:46:37 - sqlalchemy.engine.Engine - INFO - [generated in 0.00086s] ()
2025-07-11 16:46:37 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM conversations
2025-07-11 16:46:37 - sqlalchemy.engine.Engine - INFO - [generated in 0.00245s] ()
2025-07-11 16:46:37 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-11 16:46:37 - ai_companion.main - INFO - 🛑 Shutting down AI Companion System...
2025-07-11 16:46:37 - ai_companion.services.storage - INFO - Storage connections closed
2025-07-11 16:46:37 - ai_companion.main - INFO - ✅ System shutdown complete
2025-07-11 16:47:36 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:47:36 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:47:36 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 16:47:36 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 16:47:36 - ai_companion.main - INFO - 🚀 Initializing AI Companion System...
2025-07-11 16:47:36 - ai_companion.main - INFO - ✅ Configuration validated
2025-07-11 16:47:36 - ai_companion.main - INFO - 🧠 Initializing core AI services...
2025-07-11 16:47:36 - ai_companion.services.gemini - INFO - ✅ Gemini model 'gemini-1.5-flash' initialized successfully
2025-07-11 16:47:36 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 16:47:36 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_profiles")
2025-07-11 16:47:36 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:47:36 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("personal_memories")
2025-07-11 16:47:36 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:47:36 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("universal_memories")
2025-07-11 16:47:36 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:47:36 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversations")
2025-07-11 16:47:36 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 16:47:36 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-11 16:47:36 - ai_companion.services.storage - INFO - ✅ Database initialized at data\db\ai_companion.db
2025-07-11 16:47:36 - ai_companion.core.emotions - INFO - ✅ Emotional Intelligence Service initialized
2025-07-11 16:47:37 - ai_companion.services.gemini - ERROR - Error generating response: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 23
}
]
2025-07-11 16:47:37 - ai_companion.main - WARNING - ⚠️ Gemini service test failed - may be quota limited
2025-07-11 16:47:37 - ai_companion.services.storage - ERROR - Error storing personal memory: 'str' object has no attribute 'value'
2025-07-11 16:47:38 - ai_companion.services.gemini - ERROR - Error analyzing emotional content: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 22
}
]
2025-07-11 16:47:38 - ai_companion.main - INFO - ✅ All core services tested successfully
2025-07-11 16:47:38 - ai_companion.main - INFO - ✅ Core services initialized
2025-07-11 16:47:38 - ai_companion.main - INFO - 🏥 Initializing mental health services...
2025-07-11 16:47:38 - ai_companion.mental_health.crisis_detection - INFO - ✅ Crisis Detection Service initialized
2025-07-11 16:47:38 - ai_companion.mental_health.privacy - WARNING - Generated new encryption key - store this securely in production!
2025-07-11 16:47:38 - ai_companion.mental_health.privacy - INFO - ✅ Data Anonymizer initialized
2025-07-11 16:47:38 - ai_companion.mental_health.analytics - INFO - ✅ Mental Health Analytics Service initialized
2025-07-11 16:47:38 - ai_companion.main - INFO - ✅ Mental health services initialized
2025-07-11 16:47:38 - ai_companion.main - INFO - 🌐 Initializing interfaces...
2025-07-11 16:47:38 - ai_companion.interfaces.gradio_app - INFO - ✅ Gradio interface initialized
2025-07-11 16:47:38 - ai_companion.main - INFO - ✅ Interfaces initialized
2025-07-11 16:47:38 - ai_companion.main - INFO - 🎉 AI Companion System ready in 2.31 seconds!
2025-07-11 16:47:39 - ai_companion.services.gemini - ERROR - Error generating response: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 21
}
]
2025-07-11 16:47:40 - ai_companion.services.gemini - ERROR - Error analyzing emotional content: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 20
}
]
2025-07-11 16:47:40 - ai_companion.services.storage - ERROR - Error storing personal memory: 'str' object has no attribute 'value'
2025-07-11 16:47:40 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 16:47:40 - sqlalchemy.engine.Engine - INFO - SELECT * FROM personal_memories WHERE user_id = ? ORDER BY importance_score DESC, timestamp DESC LIMIT ?
2025-07-11 16:47:40 - sqlalchemy.engine.Engine - INFO - [generated in 0.00109s] ('test_user', 5)
2025-07-11 16:47:40 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-11 16:47:40 - ai_companion.core.conversation - INFO - Started conversation c7057002-a047-4494-9137-e91908321542 for user test_user
2025-07-11 16:47:40 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-11 16:47:40 - ai_companion.services.gemini - ERROR - Error analyzing emotional content: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 19
}
]
2025-07-11 16:47:40 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 16:47:40 - sqlalchemy.engine.Engine - INFO - SELECT * FROM universal_memories ORDER BY effectiveness_score DESC, usage_count DESC LIMIT ?
2025-07-11 16:47:40 - sqlalchemy.engine.Engine - INFO - [generated in 0.00084s] (20,)
2025-07-11 16:47:40 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-11 16:47:42 - ai_companion.services.gemini - ERROR - Error generating response: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 18
}
]
2025-07-11 16:47:42 - ai_companion.core.conversation - ERROR - Error processing message for user test_user: 1 validation error for ConversationMessage
content
  Input should be a valid string [type=string_type, input_value=None, input_type=NoneType]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
2025-07-11 16:47:42 - ai_companion.services.gemini - ERROR - Error analyzing emotional content: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 18
}
]
2025-07-11 16:47:43 - ai_companion.services.gemini - ERROR - Error generating therapeutic response: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {
}
, links {
  description: "Learn more about Gemini API quotas"
  url: "https://ai.google.dev/gemini-api/docs/rate-limits"
}
, retry_delay {
  seconds: 17
}
]
2025-07-11 16:47:43 - ai_companion.mental_health.crisis_detection - WARNING - Crisis event tracked for user test_user: suicide_ideation
2025-07-11 16:47:43 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 16:47:43 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM user_profiles
2025-07-11 16:47:43 - sqlalchemy.engine.Engine - INFO - [generated in 0.00109s] ()
2025-07-11 16:47:43 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM personal_memories
2025-07-11 16:47:43 - sqlalchemy.engine.Engine - INFO - [generated in 0.00099s] ()
2025-07-11 16:47:43 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM universal_memories
2025-07-11 16:47:43 - sqlalchemy.engine.Engine - INFO - [generated in 0.00108s] ()
2025-07-11 16:47:43 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM conversations
2025-07-11 16:47:43 - sqlalchemy.engine.Engine - INFO - [generated in 0.00234s] ()
2025-07-11 16:47:43 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-11 16:47:43 - ai_companion.main - INFO - 🛑 Shutting down AI Companion System...
2025-07-11 16:47:43 - ai_companion.services.storage - INFO - Storage connections closed
2025-07-11 16:47:43 - ai_companion.main - INFO - ✅ System shutdown complete
2025-07-11 17:15:59 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 17:15:59 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 17:15:59 - ai_companion.utils.logging - INFO - Logging configured - Level: INFO, File: data\logs\ai_companion.log
2025-07-11 17:15:59 - ai_companion.main - INFO - AI Companion System initialized
2025-07-11 17:15:59 - ai_companion.main - INFO - 🚀 Initializing AI Companion System...
2025-07-11 17:15:59 - ai_companion.main - INFO - ✅ Configuration validated
2025-07-11 17:15:59 - ai_companion.main - INFO - 🧠 Initializing core AI services...
2025-07-11 17:15:59 - ai_companion.services.gemini - INFO - ✅ Gemini model 'gemini-1.5-flash' initialized successfully
2025-07-11 17:16:00 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 17:16:00 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("user_profiles")
2025-07-11 17:16:00 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 17:16:00 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("personal_memories")
2025-07-11 17:16:00 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 17:16:00 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("universal_memories")
2025-07-11 17:16:00 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 17:16:00 - sqlalchemy.engine.Engine - INFO - PRAGMA main.table_info("conversations")
2025-07-11 17:16:00 - sqlalchemy.engine.Engine - INFO - [raw sql] ()
2025-07-11 17:16:00 - sqlalchemy.engine.Engine - INFO - COMMIT
2025-07-11 17:16:00 - ai_companion.services.storage - INFO - ✅ Database initialized at data\db\ai_companion.db
2025-07-11 17:16:00 - ai_companion.core.emotions - INFO - ✅ Emotional Intelligence Service initialized
2025-07-11 17:16:01 - ai_companion.services.gemini - ERROR - Error generating response: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 17:16:01 - ai_companion.main - WARNING - ⚠️ Gemini service test failed - may be quota limited
2025-07-11 17:16:01 - ai_companion.services.storage - ERROR - Error storing personal memory: 'str' object has no attribute 'value'
2025-07-11 17:16:02 - ai_companion.services.gemini - ERROR - Error analyzing emotional content: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 17:16:02 - ai_companion.main - INFO - ✅ All core services tested successfully
2025-07-11 17:16:02 - ai_companion.main - INFO - ✅ Core services initialized
2025-07-11 17:16:02 - ai_companion.main - INFO - 🏥 Initializing mental health services...
2025-07-11 17:16:02 - ai_companion.mental_health.crisis_detection - INFO - ✅ Crisis Detection Service initialized
2025-07-11 17:16:02 - ai_companion.mental_health.privacy - WARNING - Generated new encryption key - store this securely in production!
2025-07-11 17:16:02 - ai_companion.mental_health.privacy - INFO - ✅ Data Anonymizer initialized
2025-07-11 17:16:02 - ai_companion.mental_health.analytics - INFO - ✅ Mental Health Analytics Service initialized
2025-07-11 17:16:02 - ai_companion.main - INFO - ✅ Mental health services initialized
2025-07-11 17:16:02 - ai_companion.main - INFO - 🌐 Initializing interfaces...
2025-07-11 17:16:02 - ai_companion.interfaces.gradio_app - INFO - ✅ Gradio interface initialized
2025-07-11 17:16:02 - ai_companion.main - INFO - ✅ Interfaces initialized
2025-07-11 17:16:02 - ai_companion.main - INFO - 🎉 AI Companion System ready in 2.47 seconds!
2025-07-11 17:16:02 - ai_companion.services.gemini - ERROR - Error generating response: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 17:16:03 - ai_companion.services.gemini - ERROR - Error analyzing emotional content: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 17:16:03 - ai_companion.services.storage - ERROR - Error storing personal memory: 'str' object has no attribute 'value'
2025-07-11 17:16:03 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 17:16:03 - sqlalchemy.engine.Engine - INFO - SELECT * FROM personal_memories WHERE user_id = ? ORDER BY importance_score DESC, timestamp DESC LIMIT ?
2025-07-11 17:16:03 - sqlalchemy.engine.Engine - INFO - [generated in 0.00390s] ('test_user', 5)
2025-07-11 17:16:03 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-11 17:16:03 - ai_companion.core.conversation - INFO - Started conversation c32919b5-ac01-4bf9-b872-68ca0936cecc for user test_user
2025-07-11 17:16:05 - httpx - INFO - HTTP Request: GET https://api.gradio.app/pkg-version "HTTP/1.1 200 OK"
2025-07-11 17:16:05 - ai_companion.services.gemini - ERROR - Error analyzing emotional content: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 17:16:05 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 17:16:05 - sqlalchemy.engine.Engine - INFO - SELECT * FROM universal_memories ORDER BY effectiveness_score DESC, usage_count DESC LIMIT ?
2025-07-11 17:16:05 - sqlalchemy.engine.Engine - INFO - [generated in 0.00102s] (20,)
2025-07-11 17:16:05 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-11 17:16:06 - ai_companion.services.gemini - ERROR - Error generating response: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 17:16:06 - ai_companion.core.conversation - ERROR - Error processing message for user test_user: 1 validation error for ConversationMessage
content
  Input should be a valid string [type=string_type, input_value=None, input_type=NoneType]
    For further information visit https://errors.pydantic.dev/2.5/v/string_type
2025-07-11 17:16:06 - ai_companion.services.gemini - ERROR - Error analyzing emotional content: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 17:16:07 - ai_companion.services.gemini - ERROR - Error generating therapeutic response: 400 API key not valid. Please pass a valid API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key not valid. Please pass a valid API key."
]
2025-07-11 17:16:07 - ai_companion.mental_health.crisis_detection - WARNING - Crisis event tracked for user test_user: suicide_ideation
2025-07-11 17:16:07 - sqlalchemy.engine.Engine - INFO - BEGIN (implicit)
2025-07-11 17:16:07 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM user_profiles
2025-07-11 17:16:07 - sqlalchemy.engine.Engine - INFO - [generated in 0.00097s] ()
2025-07-11 17:16:07 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM personal_memories
2025-07-11 17:16:07 - sqlalchemy.engine.Engine - INFO - [generated in 0.00095s] ()
2025-07-11 17:16:07 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM universal_memories
2025-07-11 17:16:07 - sqlalchemy.engine.Engine - INFO - [generated in 0.00065s] ()
2025-07-11 17:16:07 - sqlalchemy.engine.Engine - INFO - SELECT COUNT(*) FROM conversations
2025-07-11 17:16:07 - sqlalchemy.engine.Engine - INFO - [generated in 0.00061s] ()
2025-07-11 17:16:07 - sqlalchemy.engine.Engine - INFO - ROLLBACK
2025-07-11 17:16:07 - ai_companion.main - INFO - 🛑 Shutting down AI Companion System...
2025-07-11 17:16:07 - ai_companion.services.storage - INFO - Storage connections closed
2025-07-11 17:16:07 - ai_companion.main - INFO - ✅ System shutdown complete
