# 🧠 AI Companion System

**A Production-Ready Conversational AI for Emotional Support and Mental Health**

An advanced AI companion system featuring sophisticated emotional intelligence, dual-memory architecture, crisis detection, and therapeutic conversation capabilities. Built with Google Gemini API and designed for scalable deployment with comprehensive mental health support features.

## ✨ Key Features

### 🧠 Advanced AI Capabilities
- **Emotional Intelligence**: Real-time emotion detection with confidence scoring and empathetic responses
- **Dual-Memory Architecture**: Personal user memories + universal shared knowledge with intelligent retrieval
- **Crisis Detection**: Automated mental health risk assessment with immediate intervention protocols
- **Therapeutic Techniques**: Evidence-based conversation strategies including CBT, validation, and mindfulness

### 🚀 Production-Ready Architecture
- **High Performance**: Sub-300ms response times with intelligent caching and async processing
- **Scalable Design**: Supports 100+ concurrent users with microservices architecture
- **Comprehensive Monitoring**: Real-time metrics, health checks, and performance tracking
- **Security First**: End-to-end encryption, privacy protection, and GDPR compliance

### 🌐 Multiple Interfaces
- **Gradio Web Interface**: Beautiful, responsive UI for development and user interaction
- **FastAPI REST API**: Production-grade API with comprehensive documentation
- **WhatsApp Bot Integration**: 24/7 support through WhatsApp Business API
- **CLI Tools**: Command-line interface for administration and testing

### 🏥 Mental Health Focus
- **Privacy-First Design**: GDPR/HIPAA compliant data handling with anonymization
- **Crisis Intervention**: Immediate support protocols for high-risk situations
- **Analytics Platform**: Anonymized insights for mental health research
- **Professional Integration**: Tools for therapists, researchers, and healthcare providers

## 🏗️ Technical Architecture

### System Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    🌐 Interface Layer                       │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Gradio UI     │   FastAPI       │   WhatsApp Bot          │
│   (Development) │   (Production)  │   (Optional)            │
│   Port: 7860    │   Port: 8000    │   Webhook-based         │
└─────────────────┴─────────────────┴─────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   🧠 Core AI Services                       │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Conversation  │   Memory        │   Emotional             │
│   Orchestrator  │   Manager       │   Intelligence          │
│   (async)       │   (dual-arch)   │   (Gemini + patterns)   │
└─────────────────┴─────────────────┴─────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                🏥 Mental Health Platform                    │
├─────────────────┬─────────────────┬─────────────────────────┤
│   Crisis        │   Analytics     │   Privacy               │
│   Detection     │   Engine        │   Protection            │
│   (real-time)   │   (anonymized)  │   (encryption)          │
└─────────────────┴─────────────────┴─────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   💾 Data Layer                             │
├─────────────────┬─────────────────┬─────────────────────────┤
│   SQLite/       │   Redis         │   File                  │
│   PostgreSQL    │   Cache         │   Storage               │
│   (persistent)  │   (sessions)    │   (logs/backups)        │
└─────────────────┴─────────────────┴─────────────────────────┘
```

### How Components Interact

1. **🎯 Message Flow**: User → Interface → Conversation Service → AI Processing → Response
2. **🧠 Memory Integration**: Every interaction updates both personal and universal memory
3. **💝 Emotion Analysis**: Real-time emotion detection influences response generation
4. **🚨 Crisis Detection**: Continuous monitoring with immediate intervention protocols
5. **📊 Analytics**: Anonymized data collection for research and improvement

## 🚀 Quick Start

### 1. Prerequisites

- **Python 3.9+** (3.11 recommended)
- **Google Gemini API key** (get from [Google AI Studio](https://makersuite.google.com/app/apikey))
- **Git** for version control
- **Redis** (optional, for enhanced caching)

### 2. Automated Setup (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd ai-companion-system

# Run automated development setup
python scripts/dev_setup.py
```

### 3. Manual Setup (Alternative)

```bash
# Create and activate virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt  # For development

# Create necessary directories
mkdir -p data/{db,logs,cache}
```

### 4. Configuration

The system comes with a working `.env` file, but you should verify your Gemini API key:

```bash
# Check your .env file
cat .env

# Update your Gemini API key if needed
GEMINI_API_KEY=your_actual_api_key_here
```

### 5. Validate Setup

```bash
# Run system validation tests
python test_system.py

# Or use the Makefile
make test-system
```

### 6. Start the System

```bash
# Option 1: Run with Makefile (recommended)
make run

# Option 2: Run directly
python -m src.ai_companion.main

# Option 3: Run with Docker
docker-compose up
```

### 7. Access the Interfaces

- **🌐 Web Interface**: http://localhost:7860 (Gradio UI)
- **📚 API Documentation**: http://localhost:8000/docs (FastAPI)
- **💚 Health Check**: http://localhost:8000/health
- **📊 Metrics**: http://localhost:8000/metrics

## 🧪 Testing & Validation

### Quick System Test

```bash
# Run comprehensive system validation
python test_system.py

# Expected output: All tests should pass
# 🎉 ALL TESTS PASSED! System is ready for use.
```

### Development Testing

```bash
# Using Makefile (recommended)
make test              # Run all tests
make test-unit         # Unit tests only
make test-integration  # Integration tests only
make test-performance  # Performance tests only
make test-coverage     # Tests with coverage report

# Using pytest directly
pytest tests/ -v                    # All tests
pytest tests/unit/ -v               # Unit tests
pytest tests/integration/ -v        # Integration tests
pytest --cov=src/ai_companion       # Coverage report
```

### Test Categories

- **🔧 Unit Tests**: Individual component testing (models, services, utilities)
- **🔗 Integration Tests**: Service interaction testing (API, database, memory)
- **⚡ Performance Tests**: Response time and load testing (< 300ms target)
- **🔒 Security Tests**: Data protection and privacy validation
- **🏥 Mental Health Tests**: Crisis detection and therapeutic response validation

### Performance Benchmarks

- **⚡ Response Time**: < 300ms average (target: 200ms)
- **💾 Memory Usage**: < 500MB under normal load
- **🎯 Cache Hit Rate**: > 80% for repeated interactions
- **👥 Concurrent Users**: 100+ simultaneous conversations
- **🧠 Emotion Detection**: > 85% accuracy on test dataset

## ⚙️ Configuration

### Environment Variables

All configuration is done through environment variables. See `.env.example` for all available options.

#### Core Settings
```env
# AI Configuration
GEMINI_API_KEY=your_api_key
RESPONSE_TEMPERATURE=0.7
MAX_TOKENS=150

# Memory Configuration
MEMORY_TTL=2592000              # 30 days
PERSONAL_MEMORY_SIZE=1000
UNIVERSAL_MEMORY_SIZE=10000

# Performance Settings
CACHE_SIZE=1000
TARGET_RESPONSE_TIME=0.3
TARGET_CACHE_HIT_RATE=0.8
```

#### Mental Health Settings
```env
CRISIS_THRESHOLD=0.7
MODERATE_RISK_THRESHOLD=0.4
ENABLE_MENTAL_HEALTH_PLATFORM=true
ANONYMIZATION_ENABLED=true
```

#### Security Settings
```env
SECRET_KEY=your-secret-key-32-chars-minimum
ALLOWED_HOSTS=localhost,127.0.0.1
CORS_ORIGINS=http://localhost:3000
```

## 🧠 Memory Architecture

The AI Companion uses a sophisticated dual-memory system inspired by cognitive science:

### Memory Types
- **Personal Memory**: User-specific memories, strictly isolated per user
- **Universal Memory**: Shared knowledge across all users (anonymized)
- **Working Memory**: Short-term context for active conversations
- **Episodic Memory**: Specific conversation events with rich context

### Memory Features
- **Intelligent Retrieval**: Context-aware memory search and ranking
- **Emotional Weighting**: Emotionally significant memories are prioritized
- **Automatic Cleanup**: Old, unused memories are gradually forgotten
- **Privacy Protection**: Personal memories are never shared between users

### Example Usage
```python
from ai_companion.core.memory import MemoryService

# Store a memory
memory = await memory_service.store_memory(
    user_id="user123",
    content="User mentioned feeling anxious about work presentation",
    interaction_type="emotion",
    emotion="anxiety"
)

# Retrieve relevant memories
memories = await memory_service.retrieve_memories(
    user_id="user123",
    query="work stress",
    limit=5
)
```

## 💝 Emotional Intelligence

The system provides advanced emotional understanding and support:

### Emotion Detection
- **Real-time Analysis**: Detects emotions from text with high accuracy
- **Multi-dimensional**: Analyzes valence, arousal, and emotional intensity
- **Context-aware**: Considers conversation history and user patterns
- **Confidence Scoring**: Provides reliability metrics for detections

### Therapeutic Techniques
- **Active Listening**: Reflective and empathetic responses
- **Validation**: Acknowledging and normalizing user emotions
- **Cognitive Reframing**: Helping users see situations differently
- **Mindfulness**: Grounding techniques for anxiety and stress
- **Crisis Intervention**: Immediate support for high-risk situations

### Crisis Detection
```python
from ai_companion.mental_health.crisis_detection import CrisisDetectionService

# Analyze mental health risk
risk_assessment = await crisis_detection.assess_risk(
    user_id="user123",
    message="I can't handle this anymore",
    emotional_state=emotional_state
)

if risk_assessment.risk_level == "critical":
    # Trigger immediate intervention
    response = await crisis_detection.generate_crisis_response(
        risk_assessment
    )
```

## 🔒 Privacy & Security

### Data Protection
- **Encryption**: All data encrypted in transit and at rest
- **User Isolation**: Personal memories strictly separated between users
- **Anonymization**: Research data is anonymized and aggregated
- **Secure Storage**: Industry-standard security practices
- **GDPR Compliance**: Full compliance with privacy regulations

### Mental Health Ethics
- **Professional Disclaimer**: Not a replacement for professional therapy
- **Crisis Protocols**: Clear escalation procedures for high-risk situations
- **Informed Consent**: Transparent data usage policies
- **Research Ethics**: Anonymized insights for mental health research

### Security Features
- **Rate Limiting**: Protection against abuse and spam
- **Input Validation**: Sanitization of all user inputs
- **Audit Logging**: Comprehensive security event logging
- **Access Controls**: Role-based permissions and authentication

## 🎯 Use Cases

### Individual Users
- **Emotional Support**: 24/7 availability for conversation and support
- **Mental Health Monitoring**: Track emotional patterns and well-being
- **Crisis Support**: Immediate intervention during difficult times
- **Personal Growth**: Develop emotional intelligence and coping skills

### Healthcare Professionals
- **Patient Monitoring**: Track patient emotional states between sessions
- **Crisis Alerts**: Immediate notification of high-risk situations
- **Research Insights**: Anonymized data for mental health research
- **Treatment Support**: Complement traditional therapy approaches

### Organizations
- **Employee Wellness**: Corporate mental health support programs
- **Educational Support**: Student emotional well-being monitoring
- **Community Health**: Scalable mental health resources
- **Research Platforms**: Population-level mental health insights

## 🚀 Deployment Options

### 🔧 Local Development

```bash
# Quick start (recommended)
make run

# Direct execution
python -m src.ai_companion.main

# With hot reloading
uvicorn src.ai_companion.interfaces.api:app --reload --host 0.0.0.0 --port 8000
```

### 🐳 Docker Deployment

```bash
# Development with Docker Compose
docker-compose -f docker-compose.dev.yml up

# Production Docker
docker build -t ai-companion .
docker run -d \
  -p 7860:7860 \
  -p 8000:8000 \
  --env-file .env \
  --name ai-companion \
  ai-companion

# With Redis and monitoring
docker-compose up -d
```

### ☁️ Cloud Deployment

#### **Render (Recommended)**
```bash
# 1. Connect GitHub repository
# 2. Set environment variables in Render dashboard
# 3. Deploy automatically on push
```

#### **Railway**
```bash
# 1. Install Railway CLI
npm install -g @railway/cli

# 2. Deploy
railway login
railway init
railway up
```

#### **Google Cloud Run**
```bash
# 1. Build and push to Container Registry
gcloud builds submit --tag gcr.io/PROJECT_ID/ai-companion

# 2. Deploy to Cloud Run
gcloud run deploy --image gcr.io/PROJECT_ID/ai-companion --platform managed
```

#### **Heroku**
```bash
# 1. Create Heroku app
heroku create your-ai-companion

# 2. Set environment variables
heroku config:set GEMINI_API_KEY=your_key

# 3. Deploy
git push heroku main
```

### 🔒 Production Checklist

- [ ] Set `ENVIRONMENT=production` in `.env`
- [ ] Configure secure `SECRET_KEY`
- [ ] Set up SSL/TLS certificates
- [ ] Configure proper logging levels
- [ ] Set up monitoring and alerts
- [ ] Configure backup strategies
- [ ] Review security settings
- [ ] Test crisis detection workflows

## 📊 Monitoring & Health

### Health Checks
```bash
# Check system health
curl http://localhost:8000/health

# Get performance metrics
curl http://localhost:8000/metrics
```

### Performance Monitoring
- **Response Time**: Average and 95th percentile tracking
- **Memory Usage**: Real-time memory consumption monitoring
- **Cache Performance**: Hit rates and efficiency metrics
- **Error Tracking**: Comprehensive error logging and alerting

### Logging
- **Application Logs**: `data/logs/ai_companion.log`
- **Performance Logs**: `data/logs/performance.log`
- **Security Logs**: `data/logs/security.log`
- **Error Logs**: Integrated with application logs

### Metrics Dashboard
Access real-time metrics at:
- **Health Status**: `/health`
- **Performance Metrics**: `/metrics`
- **System Info**: `/info`

## 🛠️ Development Guide

### Project Architecture

```
📁 AI Companion System
├── 🧠 src/ai_companion/           # Main application code
│   ├── 🎯 core/                   # Core business logic
│   │   ├── conversation.py        # Main conversation orchestration
│   │   ├── memory.py             # Dual-memory architecture
│   │   ├── emotions.py           # Emotional intelligence engine
│   │   └── models.py             # Data models and schemas
│   ├── 🔧 services/              # External service integrations
│   │   ├── gemini.py            # Google Gemini API client
│   │   ├── storage.py           # Database and Redis management
│   │   └── whatsapp.py          # WhatsApp Business API
│   ├── 🌐 interfaces/            # User interfaces
│   │   ├── gradio_app.py        # Web UI (Gradio)
│   │   ├── api.py               # REST API (FastAPI)
│   │   └── cli.py               # Command-line interface
│   ├── 🏥 mental_health/         # Mental health specialized features
│   │   ├── crisis_detection.py  # Crisis intervention system
│   │   ├── analytics.py         # Mental health analytics
│   │   └── privacy.py           # Privacy and anonymization
│   ├── ⚙️ config/               # Configuration management
│   │   └── settings.py          # Pydantic settings
│   └── 🔨 utils/                # Utilities and helpers
│       ├── logging.py           # Structured logging
│       ├── monitoring.py        # Performance monitoring
│       └── helpers.py           # Common utilities
├── 🧪 tests/                     # Comprehensive test suite
│   ├── unit/                    # Unit tests
│   ├── integration/             # Integration tests
│   └── performance/             # Performance tests
├── 📊 data/                      # Data storage
│   ├── db/                      # Database files
│   ├── logs/                    # Application logs
│   └── cache/                   # Temporary cache
├── 📚 docs/                      # Documentation
├── 🐳 Docker files              # Containerization
└── 🔧 Development tools         # Scripts, configs, etc.
```

### Development Workflow

```bash
# 1. Setup development environment
make setup                    # Complete development setup
source venv/bin/activate     # Activate virtual environment

# 2. Development cycle
make format                  # Format code (black, isort)
make lint                   # Check code quality (flake8, mypy)
make test                   # Run tests
make run                    # Start development server

# 3. Advanced development
make test-coverage          # Generate coverage report
make docker-build          # Build Docker image
make clean                  # Clean temporary files
```

### Code Quality Standards

- **🎯 Type Safety**: All functions must have proper type annotations
- **📝 Documentation**: Comprehensive docstrings for all public APIs
- **🧪 Testing**: Minimum 80% test coverage for new code
- **🎨 Formatting**: Black (line length: 100) and isort for imports
- **🔍 Linting**: Must pass flake8 and mypy without errors
- **🔒 Security**: Input validation and secure coding practices

### Contributing Guidelines

1. **🍴 Fork** the repository and create a feature branch
2. **✍️ Write** comprehensive tests for new functionality
3. **🎨 Format** code using `make format`
4. **🔍 Lint** code using `make lint`
5. **🧪 Test** thoroughly using `make test`
6. **📝 Document** changes and update README if needed
7. **🚀 Submit** a pull request with detailed description

## 📈 Roadmap

### Version 1.1 (Next Release)
- **Enhanced WhatsApp Integration**: Full production WhatsApp bot
- **Advanced Analytics Dashboard**: Real-time insights and reporting
- **Multi-language Support**: Support for Spanish, French, German
- **Voice Integration**: Speech-to-text and text-to-speech capabilities

### Version 1.2 (Future)
- **Mobile Applications**: Native iOS and Android apps
- **Professional Dashboard**: Tools for therapists and researchers
- **Group Therapy Features**: Multi-user therapeutic sessions
- **Advanced AI Models**: Integration with latest language models

### Long-term Vision
- **Healthcare Integration**: EHR and clinical system integrations
- **Research Platform**: Federated learning for privacy-preserving research
- **Global Deployment**: Multi-region, multi-language support
- **AI Advancement**: Cutting-edge emotional AI and therapeutic techniques

## 🆘 Crisis Support Resources

### Immediate Help
- **National Suicide Prevention Lifeline**: 988 (US)
- **Crisis Text Line**: Text HOME to 741741 (US)
- **International Association for Suicide Prevention**: https://www.iasp.info/resources/Crisis_Centres/
- **Emergency Services**: 911 (US), 999 (UK), 112 (EU)

### Important Disclaimer
This AI companion is designed to provide emotional support and early intervention, but it is **not a replacement** for professional mental health care. Always consult with qualified mental health professionals for serious concerns.

## 🔧 Troubleshooting

### Common Issues

#### **🔑 API Key Issues**
```bash
# Problem: "Invalid API key" or "Quota exceeded"
# Solution: Check your Gemini API key
echo $GEMINI_API_KEY
# Get a new key from: https://makersuite.google.com/app/apikey
```

#### **📦 Import Errors**
```bash
# Problem: "ModuleNotFoundError"
# Solution: Install dependencies and check Python path
pip install -r requirements.txt
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"
```

#### **🗄️ Database Issues**
```bash
# Problem: Database connection errors
# Solution: Check database path and permissions
mkdir -p data/db
chmod 755 data/db
```

#### **🚀 Performance Issues**
```bash
# Problem: Slow response times
# Solution: Enable Redis caching
# Set REDIS_ENABLED=true in .env
# Install Redis: https://redis.io/download
```

#### **🔒 Permission Errors**
```bash
# Problem: Permission denied errors
# Solution: Fix file permissions
chmod +x scripts/*.py
chown -R $USER:$USER data/
```

### Debug Mode

```bash
# Enable debug logging
export DEBUG_MODE=true
export LOG_LEVEL=DEBUG

# Run with verbose output
python -m src.ai_companion.main --verbose

# Check logs
tail -f data/logs/ai_companion.log
```

### Health Checks

```bash
# System health check
curl http://localhost:8000/health

# Detailed system status
python test_system.py

# Performance metrics
curl http://localhost:8000/metrics
```

## 📞 Support & Community

### Getting Help
- **📚 Documentation**: Comprehensive guides in the `docs/` directory
- **🐛 Issues**: Report bugs and request features on GitHub Issues
- **💬 Discussions**: Join community discussions for help and feedback
- **📧 Email**: Contact the development team for enterprise support

### Contributing
We welcome contributions! Please see our contributing guidelines and feel free to:
- 🐛 Report bugs and suggest features
- 🔧 Submit pull requests for improvements
- 📝 Help with documentation and testing
- 💡 Share your use cases and feedback
- 🌟 Star the repository if you find it useful

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

### Privacy & Compliance
- **Data Minimization**: Only necessary data is collected
- **User Control**: Users own and control their data
- **Transparency**: Clear data usage and privacy policies
- **Compliance**: Designed with GDPR and privacy regulations in mind

---

## 🎉 Getting Started

Ready to deploy your AI Companion System? Follow the [Quick Start](#-quick-start) guide above, and you'll have a production-ready emotional AI system running in minutes.

For questions, issues, or contributions, please visit our [GitHub repository](https://github.com/ai-companion/ai-companion-system).

---

*Built with ❤️ for human emotional wellbeing and mental health support.*